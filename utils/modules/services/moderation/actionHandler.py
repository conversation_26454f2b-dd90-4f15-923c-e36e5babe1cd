from __future__ import annotations

from typing import TYPE_CHECKING, Optional, Callable, TypedDict

import discord

from utils.constants import logger
from utils.modules.core.db.models import Hub, InfractionType
from utils.modules.core.messageDelete import MessageDeleteResult, delete_interchat_message
from utils.modules.core.i18n import t
from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.moderation import fetch_original_message
from utils.modules.events.hubLoggingHelpers import HubLogger
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.moderationService import NO_REASON, ModerationService
from utils.modules.services.permission_service import PermissionService
from utils.utils import ms_to_human

from .types import ActionType, ModerationTarget

if TYPE_CHECKING:
    from discord.ext import commands
    from main import Bot

    class SendKwargs(TypedDict, total=False):
        content: str
        embed: discord.Embed


class ModerationActionHandler:
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        selected_hub: Hub,
        locale: str,
    ):
        self.bot = bot
        self.moderator = moderator
        self.hub = selected_hub
        self.locale = locale
        self.constants = bot.constants

    async def _send_error(self, ctx: 'discord.Interaction | commands.Context', message: str):
        """Send a standardized error message."""
        embed = discord.Embed(
            title=t('ui.common.titles.error', locale=self.locale),
            description=f'{self.bot.emotes.x_icon} {message}',
            color=discord.Color.red(),
        )
        await self.send_message(ctx, embed=embed, ephemeral=True)

    async def _send_success(self, ctx: 'discord.Interaction | commands.Context', message: str):
        """Send a standardized success message."""
        embed = discord.Embed(
            title=t('ui.common.titles.success', locale=self.locale),
            description=f'{self.bot.emotes.tick} {message}',
            color=discord.Color.green(),
        )
        await self.send_message(ctx, embed=embed, ephemeral=True)

    async def _ensure_permission(
        self,
        ctx: 'discord.Interaction | commands.Context',
        required_level: HubPermissionLevel = HubPermissionLevel.MODERATOR,
    ) -> bool:
        """Check if the moderator has the required permissions."""
        async with self.bot.db.get_session() as session:
            perm_service = PermissionService(session)
            has_perm, actual_level = await perm_service.check_permission_from_hub(
                self.hub, str(self.moderator.id), required_level
            )

        logger.debug(
            f'Permission check for user {self.moderator.id} on hub {self.hub.id}: '
            f'actual={actual_level.name}, required={required_level.name}'
        )
        if not has_perm:
            permission_name = required_level.name.title()
            await self._send_error(
                ctx,
                t(
                    'responses.infractions.permissions.insufficient',
                    self.locale,
                    permission=permission_name,
                ),
            )
            return False
        return True

    def _validate_target(self, target: ModerationTarget) -> Optional[str]:
        """Validate the moderation target, returning an error message if invalid."""
        is_valid, msg = target.validate()
        return None if is_valid else msg

    async def _check_existing_infraction(
        self, modsvc: ModerationService, target: ModerationTarget, infraction_type: InfractionType
    ) -> bool:
        """Check if the target already has an active infraction of the given type."""
        active = await modsvc.get_active_infractions(
            self.hub.id,
            user_id=target.target_id if target.is_user else None,
            server_id=target.target_id if target.is_server else None,
        )
        return any(inf.type == infraction_type for inf in active)

    async def _create_infraction(
        self,
        modsvc: ModerationService,
        target: ModerationTarget,
        infraction_type: InfractionType,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Create a new infraction."""
        return await modsvc.create_infraction(
            hub_id=self.hub.id,
            mod_id=str(self.moderator.id),
            infraction_type=infraction_type,
            reason=reason or NO_REASON,
            duration_ms=duration_ms,
            user_id=target.target_id if target.is_user else None,
            server_id=target.target_id if target.is_server else None,
            server_name=target.target_name if target.is_server else None,
        )

    async def _log_action(
        self, action: ActionType, target: ModerationTarget, reason: str, expires_at=None
    ):
        """Log the moderation action."""
        log_map: dict[ActionType, Callable] = {
            ActionType.WARN: HubLogger.log_user_warn
            if target.is_user
            else HubLogger.log_server_warn,
            ActionType.MUTE: HubLogger.log_user_mute
            if target.is_user
            else HubLogger.log_server_mute,
            ActionType.BAN: HubLogger.log_user_ban if target.is_user else HubLogger.log_server_ban,
            ActionType.UNMUTE: HubLogger.log_user_unmute
            if target.is_user
            else HubLogger.log_server_unmute,
            ActionType.UNBAN: HubLogger.log_user_unban
            if target.is_user
            else HubLogger.log_server_unban,
        }

        log_method = log_map.get(action)
        if not log_method:
            return

        log_kwargs = {
            'hub_id': self.hub.id,
            'hub_name': self.hub.name,
            'moderator_id': str(self.moderator.id),
            'moderator_name': str(self.moderator),
            'user_id': target.target_id if target.is_user else None,
            'user_name': target.target_name if target.is_user else None,
            'server_id': target.target_id if target.is_server else None,
            'server_name': target.target_name if target.is_server else None,
            'reason': reason,
        }

        if action in [ActionType.MUTE, ActionType.BAN] and expires_at is not None:
            log_kwargs['expires_at'] = expires_at

        await log_method(**log_kwargs)

    async def send_message(
        self,
        ctx: discord.Interaction | commands.Context,
        content: Optional[str] = None,
        embed: Optional[discord.Embed] = None,
        ephemeral: bool = False,
    ):
        """Send a message in response to an interaction or context."""
        kwargs: 'SendKwargs' = {}
        if content is not None:
            kwargs['content'] = content
        if embed is not None:
            kwargs['embed'] = embed

        if isinstance(ctx, discord.Interaction):
            if not ctx.response.is_done():
                await ctx.response.send_message(ephemeral=ephemeral, **kwargs)
            else:
                await ctx.followup.send(ephemeral=ephemeral, **kwargs)
        else:
            await ctx.send(**kwargs)

    async def handle_punitive_action(
        self,
        ctx: discord.Interaction | commands.Context,
        action: ActionType,
        target: ModerationTarget,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Handle punitive actions (warn, mute, ban)."""
        if error_msg := self._validate_target(target):
            return await self._send_error(ctx, error_msg)

        if not await self._ensure_permission(ctx):
            return

        action_map = {
            ActionType.WARN: InfractionType.WARNING,
            ActionType.MUTE: InfractionType.MUTE,
            ActionType.BAN: InfractionType.BAN,
        }
        infraction_type = action_map[action]
        state_map = {
            ActionType.MUTE: t('ui.moderation.actionNames.muted', locale=self.locale),
            ActionType.BAN: t('ui.moderation.actionNames.banned', locale=self.locale),
        }

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)

            if action != ActionType.WARN and await self._check_existing_infraction(
                modsvc, target, infraction_type
            ):
                target_type = (
                    t('ui.moderation.targetSelection.userField', locale=self.locale)
                    if target.is_user
                    else t('ui.moderation.targetSelection.serverField', locale=self.locale)
                )
                state = state_map[action]
                return await self._send_error(
                    ctx,
                    t(
                        'responses.moderation.errors.alreadyState',
                        locale=self.locale,
                        targetType=target_type,
                        state=state,
                    ),
                )

            try:
                inf = await self._create_infraction(
                    modsvc, target, infraction_type, reason, duration_ms
                )
                await self._log_action(action, target, inf.reason, inf.expiresAt)

                action_name_map = {
                    ActionType.WARN: t('ui.moderation.actionNames.warned', locale=self.locale),
                    ActionType.MUTE: t('ui.moderation.actionNames.muted', locale=self.locale),
                    ActionType.BAN: t('ui.moderation.actionNames.banned', locale=self.locale),
                }
                action_name = action_name_map[action]
                target_mention = (
                    target.user.mention if target.is_user and target.user else target.target_name
                )
                prep = (
                    t('ui.moderation.prep.in', locale=self.locale)
                    if action in [ActionType.WARN, ActionType.MUTE]
                    else t('ui.moderation.prep.from', locale=self.locale)
                )
                description = t(
                    'responses.moderation.success.action',
                    self.locale,
                    action=action_name.lower(),
                    target=target_mention,
                    prep=prep,
                    hubName=self.hub.name,
                )

                if action == ActionType.MUTE and duration_ms:
                    duration_str = ms_to_human(duration_ms)
                    description += f' for {duration_str}'

                await self._send_success(ctx, description)

            except ValueError:
                target_type = (
                    t('ui.moderation.targetSelection.userField', locale=self.locale)
                    if target.is_user
                    else t('ui.moderation.targetSelection.serverField', locale=self.locale)
                )
                state = state_map[action]
                await self._send_error(
                    ctx,
                    t(
                        'responses.moderation.errors.alreadyState',
                        locale=self.locale,
                        targetType=target_type,
                        state=state,
                    ),
                )

    async def handle_revoke_action(
        self,
        ctx: 'discord.Interaction | commands.Context',
        action: ActionType,
        target: ModerationTarget,
    ):
        """Handle revoke actions (unmute, unban)."""
        if error_msg := self._validate_target(target):
            return await self._send_error(ctx, error_msg)

        if not await self._ensure_permission(ctx):
            return

        action_noun_map = {
            ActionType.UNMUTE: t('ui.moderation.actionNouns.mute', locale=self.locale),
            ActionType.UNBAN: t('ui.moderation.actionNouns.ban', locale=self.locale),
        }
        action_noun = action_noun_map[action]

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            active_infractions = await modsvc.get_active_infractions(
                self.hub.id,
                user_id=target.target_id if target.is_user else None,
                server_id=target.target_id if target.is_server else None,
            )
            relevant_infractions = [
                inf
                for inf in active_infractions
                if inf.type in [InfractionType.BAN, InfractionType.MUTE]
            ]

            if not relevant_infractions:
                return await self._send_error(
                    ctx,
                    t(
                        'responses.moderation.revoke.noActive',
                        locale=self.locale,
                        action=action_noun,
                    ),
                )

            for inf in relevant_infractions:
                await modsvc.revoke_infraction(inf.id, str(self.moderator.id))

            await self._log_action(action, target, '')
            await self._send_success(
                ctx, t('responses.moderation.revoke.success', self.locale, action=action_noun)
            )

    async def handle_delete_message(
        self,
        interaction: discord.Interaction,
        message: discord.Message | None = None,
        reason: Optional[str] = None,
    ):
        """Handle message deletion."""
        await interaction.response.defer(thinking=True, ephemeral=True)

        if not message:
            return await self._send_error(interaction, 'No message supplied to delete.')

        async with self.bot.db.get_session() as session:
            original_message = await fetch_original_message(session, str(message.id))

        if not original_message:
            return await self._send_error(
                interaction,
                t('responses.moderation.delete.notInterChatMessage', locale=self.locale),
            )

        result = await delete_interchat_message(
            bot=self.bot,
            message_id=original_message.id,
            reason=reason,
            moderator_id=str(self.moderator.id),
            moderator_name=str(self.moderator),
        )

        if result == MessageDeleteResult.NOT_FOUND:
            await self._send_error(
                interaction,
                t('responses.moderation.delete.notInterChatMessage', locale=self.locale),
            )
        else:
            success_msg = t(
                'responses.moderation.delete.success',
                locale=self.locale,
                messageId=original_message.id,
            )
            embed = discord.Embed(
                title=t('ui.common.titles.success', locale=self.locale),
                description=f'{self.bot.emotes.tick} {success_msg}',
                color=discord.Color.green(),
            )
            if reason:
                embed.add_field(
                    name=t('ui.moderation.fields.reason', locale=self.locale),
                    value=reason,
                    inline=False,
                )
            await self.send_message(interaction, embed=embed, ephemeral=True)

    async def handle_global_blacklist_action(
        self,
        ctx: 'discord.Interaction | commands.Context',
        target: ModerationTarget,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Handle global blacklist actions."""
        if not is_interchat_staff_direct(self.bot, self.moderator.id):
            return await self._send_error(
                ctx, t('responses.moderation.blacklist.permissionDenied', self.locale)
            )

        is_valid, error_msg = target.validate()
        if not is_valid:
            if not error_msg:
                return
            return await self._send_error(ctx, error_msg)

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            try:
                if target.is_user:
                    await modsvc.create_blacklist_entry(
                        user_id=target.target_id,
                        mod_id=str(self.moderator.id),
                        reason=reason or NO_REASON,
                        duration_ms=duration_ms,
                    )
                else:
                    await modsvc.create_server_blacklist_entry(
                        server_id=target.target_id,
                        mod_id=str(self.moderator.id),
                        reason=reason or NO_REASON,
                        duration_ms=duration_ms,
                    )
            except ValueError:
                return await self._send_error(
                    ctx, t('responses.moderation.blacklist.alreadyActive', self.locale)
                )

        mention = target.user.mention if target.is_user and target.user else target.target_name
        await self._send_success(
            ctx, t('responses.moderation.blacklist.success', self.locale, target=mention)
        )
